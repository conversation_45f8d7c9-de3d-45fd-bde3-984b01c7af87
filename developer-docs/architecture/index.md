# Architecture Documentation

This section describes the technical architecture and design decisions, as well as the technologies utilized for the project.

## Monorepo Structure

This project is organized as a monorepo using modern tooling for better code sharing, dependency management, and build orchestration:

- **[Turborepo](https://turborepo.com/)** - Build system and task runner with intelligent caching
- **[pnpm](https://pnpm.io/)** - Fast, disk space efficient package manager with workspace support

### Technology Stack

#### Frontend Technologies

- **[Nuxt 3](https://nuxt.com/)** - Vue.js framework for building modern web applications
- **[Vue 3](https://vuejs.org/)** - Progressive JavaScript framework with Composition API
- **[Nuxt UI 3](https://ui.nuxt.com/)** - Component library built on top of Tailwind CSS
- **[TailwindCSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **[Pinia](https://pinia.vuejs.org/)** - State management for Vue applications
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript development

#### Backend Technologies

- **[NestJS](https://nestjs.com/)** - Progressive Node.js framework for building scalable server-side applications
- **[Drizzle ORM](https://orm.drizzle.team/)** - TypeScript ORM with SQL-like syntax
- **[PostgreSQL](https://www.postgresql.org/)** - Advanced open source relational database
- **[Zod](https://zod.dev/)** - TypeScript-first schema validation library

#### Development & Testing

- **[Jest](https://jestjs.io/)** - JavaScript testing framework for backend testing
- **[Vitest](https://vitest.dev/)** - Fast unit test framework for frontend testing
- **[Playwright](https://playwright.dev/)** - End-to-end testing framework
- **[ESLint](https://eslint.org/)** - Code linting and formatting
- **[Prettier](https://prettier.io/)** - Code formatting

## Project Structure

The monorepo is organized into three main directories following a feature-driven architecture:

```
turbo-rent/
├── apps/           # All applications
├── packages/       # Shared packages and utilities
└── developer-docs/ # Documentation
```

### Applications (`apps/`)

The `apps` directory contains the main user-facing applications:

- **`admin-ui/`** - Administrative interface built with Nuxt 3

  - Port: 3020 (development)
  - Technologies: Nuxt 3, Vue 3, Nuxt UI 3, TypeScript
  - Purpose: Administrative dashboard for managing the rental platform

- **`api/`** - Backend API server built with NestJS

  - Technologies: NestJS, TypeScript, PostgreSQL, Drizzle ORM
  - Purpose: RESTful API providing business logic and data access

- **`docs/`** - Documentation site built with Nuxt 3
  - Technologies: Nuxt 3, Vue 3, TypeScript
  - Purpose: User and developer documentation

### Packages (`packages/`)

The `packages` directory contains shared code organized by purpose:

#### Core Packages

- **`api/`** - Shared API utilities and common functionality

  - `common/` - Common utilities and helpers
  - `db-provider/` - Database connection and provider setup

- **`config/`** - Shared configuration packages

  - `eslint-config/` - Shared ESLint configuration
  - `tsconfig/` - Shared TypeScript configuration

- **`db/`** - Database-related packages

  - `base/` - Base database utilities
  - `drizzle/` - Drizzle ORM configuration and schemas
  - `run-push/` - Database migration utilities

- **`ui/`** - Shared UI components and design system
  - Built with Nuxt 3 and Nuxt UI 3
  - Provides reusable components across applications

#### Feature Packages (`packages/features/`)

Feature packages contain all code related to specific business domains, organized with the following structure:

- **`article/`** - Article management feature

  - `api/` - Article-related API endpoints and business logic
  - `db/` - Article database schemas and migrations
  - `ui/` - Article-related UI components and pages

- **`rental/`** - Rental management feature

  - `api/` - Rental-related API endpoints and business logic
  - `db/` - Rental database schemas and migrations
  - `ui/` - Rental-related UI components and pages

- **`user/`** - User management feature
  - `api/` - User-related API endpoints and business logic
  - `db/` - User database schemas and migrations
  - `ui/` - User-related UI components and pages

### Architecture Benefits

This feature-driven monorepo architecture provides several advantages:

1. **Code Colocation** - All code related to a specific feature (API, database, UI) is located in one place
2. **Shared Dependencies** - Common utilities and configurations are shared across the entire codebase
3. **Type Safety** - End-to-end TypeScript ensures type safety from database to UI
4. **Efficient Builds** - Turborepo's intelligent caching speeds up builds and tests
5. **Scalability** - New features can be added as self-contained packages
6. **Maintainability** - Clear separation of concerns makes the codebase easier to maintain

## Build System & Task Management

### Turborepo Configuration

The project uses Turborepo for orchestrating builds and tasks across the monorepo. Key configuration details:

- **Concurrency**: 20 parallel tasks maximum
- **Caching**: Intelligent caching based on file changes and dependencies
- **Task Dependencies**: Build tasks depend on upstream package builds (`^build`)

#### Available Tasks

- **`build`** - Builds all packages and applications

  - Outputs: `.nuxt/**`, `.output/**`, `dist/**`
  - Environment variables: Database config, ports, NODE_ENV

- **`dev`** - Starts development servers

  - Persistent processes with no caching
  - Depends on upstream builds

- **`lint`** - Runs ESLint across all packages

### Package Managementds

The project uses **pnpm workspaces** for dependency management:

```yaml
# pnpm-workspace.yaml
packages:
  - 'apps/*'
  - 'packages/*'
  - 'packages/config/*'
  - 'packages/api/*'
  - 'packages/db/*'
  - 'packages/features/article/*'
  - 'packages/features/rental/*'
  - 'packages/features/user/*'
```

#### Workspace Dependencies

Packages reference each other using workspace protocol:

- `"@turbo-rent/article-ui": "workspace:*"`
- `"@turbo-rent/common": "workspace:*"`
- `"@turbo-rent/db-provider": "workspace:*"`

## Development Environment

### Database Setup

The project includes Docker Compose configuration for local development:

```bash
# Start PostgreSQL database
pnpm db:up

# Stop database
pnpm db:down

# Stop and remove volumes
pnpm db:down:remove
```

### Development Servers

Each application runs on a different port:

- **API**: Default NestJS port
- **Admin UI**: Port 3020
- **Docs**: Configured port (see environment variables)

### Environment Variables

Key environment variables used across the monorepo:

- `NODE_ENV` - Environment (development/production)
- `DATABASE_URL` - PostgreSQL connection string
- `DATABASE_HOST`, `DATABASE_PORT`, `DATABASE_NAME` - Database connection details
- `DATABASE_USERNAME`, `DATABASE_PASSWORD` - Database credentials
- `API_PORT` - API server port
- `ADMIN_UI_PORT` - Admin UI port
- `DOCS_PORT` - Documentation site port

## Data Flow & Communication

### API Communication

- Frontend applications communicate with the NestJS API via HTTP/REST
- Shared types and schemas ensure type safety across the stack
- API endpoints are organized by feature domains

### Database Layer

- **Drizzle ORM** provides type-safe database access
- Database schemas are defined per feature in `packages/features/*/db/`
- Migrations and schema changes are managed through Drizzle

### State Management

- **Pinia** stores handle client-side state management
- Stores are organized by feature domain
- Server state is managed through API calls and caching
